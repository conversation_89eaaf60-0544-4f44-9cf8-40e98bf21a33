package com.moyu.chuanqirensheng.feature.quest

import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.snapshots.SnapshotStateList
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.debug.DebugManager
import com.moyu.chuanqirensheng.feature.draw.DrawManager
import com.moyu.chuanqirensheng.feature.gift.GiftManager
import com.moyu.chuanqirensheng.feature.newTask.SevenDayManager
import com.moyu.chuanqirensheng.feature.pvp.PvpManager
import com.moyu.chuanqirensheng.feature.pvp.ui.lastPvpRanks
import com.moyu.chuanqirensheng.feature.story.StoryManager
import com.moyu.chuanqirensheng.feature.vip.VipManager
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.anticheat.AntiCheatManager.checkQuest
import com.moyu.chuanqirensheng.sub.datastore.KEY_CHARGE_TASK
import com.moyu.chuanqirensheng.sub.datastore.KEY_COLLECT_TASK
import com.moyu.chuanqirensheng.sub.datastore.KEY_COST_TASK
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_DRAW_TASK
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_LOGIN_DAY
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_NEW_TASK_UPDATE_TIME_IN_MILLIS
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_PVP_TASK
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_PVP_TASK_UPDATE_TIME_IN_MILLIS
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_TASK
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_TASK_PROGRESS
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_TASK_UPDATE_TIME_IN_MILLIS
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_WARPASS2_TASK_UPDATE_TIME_IN_MILLIS
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_WARPASS_TASK_UPDATE_TIME_IN_MILLIS
import com.moyu.chuanqirensheng.sub.datastore.KEY_NEW_TASK
import com.moyu.chuanqirensheng.sub.datastore.KEY_ONE_TIME_GAME_TASK
import com.moyu.chuanqirensheng.sub.datastore.KEY_TASK_DIALOG_SHOWED
import com.moyu.chuanqirensheng.sub.datastore.KEY_WAR_PASS2_TASK
import com.moyu.chuanqirensheng.sub.datastore.KEY_WAR_PASS_TASK
import com.moyu.chuanqirensheng.sub.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.getIntFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.getListObject
import com.moyu.chuanqirensheng.sub.datastore.getLongFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.mapData
import com.moyu.chuanqirensheng.sub.datastore.setBooleanValueByKey
import com.moyu.chuanqirensheng.sub.datastore.setIntValueByKey
import com.moyu.chuanqirensheng.sub.datastore.setListObject
import com.moyu.chuanqirensheng.sub.datastore.setListObjectSync
import com.moyu.chuanqirensheng.sub.datastore.setLongValueByKey
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.chuanqirensheng.util.isSameDay
import com.moyu.core.model.Award
import com.moyu.core.model.Quest
import com.moyu.core.util.RANDOM
import com.moyu.core.util.subListOrEmpty
import timber.log.Timber
import java.lang.Integer.min
import kotlin.math.max

const val FOREVER = "k_)"

object QuestManager {
    val dailyTasks = mutableStateListOf<Quest>()
    val pvpTasks = mutableStateListOf<Quest>()
    val warPassTasks = mutableStateListOf<Quest>()
    val warPass2Tasks = mutableStateListOf<Quest>()
    val newTasks = mutableStateListOf<Quest>()
    val oneTimeTasks = mutableStateListOf<Quest>()

    fun init() {
        createTasks()
        createPvpTasks()
        createWarPassTasks()
        createWarPass2Tasks()
        createNewTasks()
        createOneTimeTasks()
        // 任务中,启动游戏可以直接标记已完成
        onTaskStartGameTime()
    }

    fun createOneTimeTasks() {
        if (oneTimeTasks.isEmpty()) {
            getListObject<Quest>(KEY_ONE_TIME_GAME_TASK).let { taskList ->
                val pool = repo.gameCore.getGameTaskPool()
                val tasks = taskList.mapNotNull { task ->
                    pool.firstOrNull { it.id == task.id }?.copy(done = task.done, opened = task.opened)
                }
                oneTimeTasks.addAll(tasks.distinctBy { it.id })
            }
        }
        val filteredTasks = repo.gameCore.getGameTaskPool().filter { it.isOneTimeTask() }
            .filterNot { pool -> oneTimeTasks.any { it.id == pool.id } }
        oneTimeTasks.addAll(filteredTasks)
        setListObject(KEY_ONE_TIME_GAME_TASK, oneTimeTasks)
    }

    fun createWarPassTasks() {
        if (!isNetTimeValid()) {
            // 防止作弊
            return
        }
        if (warPassTasks.isEmpty()) {
            getListObject<Quest>(KEY_WAR_PASS_TASK).let { taskList ->
                val pool = repo.gameCore.getGameTaskPool()
                val tasks = taskList.mapNotNull { task ->
                    pool.firstOrNull { it.id == task.id }?.copy(
                            done = task.done,
                            opened = task.opened,
                            needRemoveCount = task.needRemoveCount
                        )
                }
                warPassTasks.addAll(tasks.distinctBy { it.id })
            }
        }
        if (!isSameDay(
                getLongFlowByKey(KEY_GAME_WARPASS_TASK_UPDATE_TIME_IN_MILLIS),
                getCurrentTime()
            )
        ) {
            // 通行证任务，每天+4个
            val filteredTasks = repo.gameCore.getGameTaskPool()
            warPassTasks.clear()
            warPassTasks.addAll(filteredTasks.filter { it.taskType == 3 }
                .take(repo.gameCore.getWarPassQuestCount()))
            setLongValueByKey(KEY_GAME_WARPASS_TASK_UPDATE_TIME_IN_MILLIS, getCurrentTime())
        }
        setListObject(KEY_WAR_PASS_TASK, warPassTasks)
    }

    fun createWarPass2Tasks() {
        if (!isNetTimeValid()) {
            // 防止作弊
            return
        }
        if (warPass2Tasks.isEmpty()) {
            getListObject<Quest>(KEY_WAR_PASS2_TASK).let { taskList ->
                val pool = repo.gameCore.getGameTaskPool()
                val tasks = taskList.mapNotNull { task ->
                    pool.firstOrNull { it.id == task.id }
                        ?.copy(
                            done = task.done,
                            opened = task.opened,
                            needRemoveCount = task.needRemoveCount
                        )
                }
                warPass2Tasks.addAll(tasks.distinctBy { it.id })
            }
        }
        if (warPass2Tasks.isEmpty()) {
            val filteredTasks = repo.gameCore.getGameTaskPool()
            warPass2Tasks.clear()
            warPass2Tasks.addAll(filteredTasks.filter { it.taskType == 7 })
            setLongValueByKey(KEY_GAME_WARPASS2_TASK_UPDATE_TIME_IN_MILLIS, getCurrentTime())
            setListObject(KEY_WAR_PASS2_TASK, warPass2Tasks)
        }
    }

    fun createNewTasks() {
        if (!isNetTimeValid()) {
            // 防止作弊
            return
        }
        if (newTasks.isEmpty()) {
            getListObject<Quest>(KEY_NEW_TASK).let { taskList ->
                val pool = repo.gameCore.getGameTaskPool()
                val tasks = taskList.mapNotNull { task ->
                    pool.firstOrNull { it.id == task.id }
                        ?.copy(done = task.done, opened = task.opened)
                }
                newTasks.addAll(tasks.distinctBy { it.id })
            }
        }
        val canGenerateNewTasks = newTasks.isEmpty() || newTasks.all { getTaskDoneFlow(it) }
        if (canGenerateNewTasks && !isSameDay(
                getLongFlowByKey(KEY_GAME_NEW_TASK_UPDATE_TIME_IN_MILLIS), getCurrentTime()
            )
        ) {
            // 新手任务，每天+4个
            val filteredTasks = repo.gameCore.getGameTaskPool()
            val showedMaxId = newTasks.maxOfOrNull { it.id } ?: 0
            newTasks.addAll(filteredTasks.filter { it.isNewTask() }.filter {
                it.id > showedMaxId
            }.take(repo.gameCore.getNewQuestCount()).map { task ->
                // 要用永久计数，但是又要移除之前的计数
                val needRemove = if (task.isMaxRecordQuest()) {
                    0
                } else {
                    val postFix = task.subType.map {
                        if (it == 0) "" else "_$it"
                    }.reduce { acc, s -> acc + s }
                    getIntFlowByKey(FOREVER + KEY_GAME_TASK_PROGRESS + task.type + postFix, 0)
                }
                task.copy(needRemoveCount = needRemove)
            })
            // 特殊逻辑，只有刷新了，才更新，什么时候完成任务，进行刷新，什么时候才更新这个时间戳
            setLongValueByKey(KEY_GAME_NEW_TASK_UPDATE_TIME_IN_MILLIS, getCurrentTime())
        }
        setListObject(KEY_NEW_TASK, newTasks)
    }

    fun createTasks() {
        if (!isNetTimeValid()) {
            // 防止作弊
            return
        }
        if (isSameDay(
                getLongFlowByKey(KEY_GAME_TASK_UPDATE_TIME_IN_MILLIS), getCurrentTime()
            )
        ) {
            // 任务：仅同一天内更新过，才加载，否则不加载，自动会在Repo刷新
            if (dailyTasks.isEmpty()) {
                try {
                    getListObject<Quest>(KEY_GAME_TASK).let {
                        val pool = repo.gameCore.getGameTaskPool()
                        val tasks = it.mapNotNull { task ->
                            pool.firstOrNull { it.id == task.id }
                                ?.copy(done = task.done, opened = task.opened)
                        }
                        dailyTasks.addAll(tasks.distinctBy { it.id })
                    }
                } catch (e: Exception) {
                    Timber.e(e)
                    // 修改了task格式
                    clearDayTasks()
                }
            }
        } else {
            // 任务：如果已经过了一天，清理任务记录
            clearDayTasks()
            onTaskStartGameDay()
        }

        // 强制设置最少1天签到
        if (getIntFlowByKey(KEY_GAME_LOGIN_DAY) == 0) {
            setIntValueByKey(KEY_GAME_LOGIN_DAY, 1)
        }
        val filteredTasks = repo.gameCore.getGameTaskPool()
        if (dailyTasks.isEmpty()) {
            val maxGameProgress = min(
                StoryManager.getMaxAge(), getIntFlowByKey(
                    FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.GAME_PROGRESS.id + "_1",
                    1
                )
            )
            dailyTasks.addAll(filteredTasks.filter { it.isDailyTask() }.filter {
                maxGameProgress >= it.talent.first() && maxGameProgress <= it.talent[1]
            }.shuffled(RANDOM)
                .take(VipManager.getExtraDailyQuest() + repo.gameCore.getDailyQuestCount())
            )

            setLongValueByKey(KEY_GAME_TASK_UPDATE_TIME_IN_MILLIS, getCurrentTime())
            setListObject(KEY_GAME_TASK, dailyTasks)
        }
    }

    fun createPvpTasks() {
        if (!isNetTimeValid()) {
            // 防止作弊
            return
        }
        if (isSameDay(
                getLongFlowByKey(KEY_GAME_PVP_TASK_UPDATE_TIME_IN_MILLIS), getCurrentTime()
            )
        ) {
            // 任务：仅同一天内更新过，才加载，否则不加载，自动会在Repo刷新
            if (pvpTasks.isEmpty()) {
                try {
                    getListObject<Quest>(KEY_GAME_PVP_TASK).let {
                        val pool = repo.gameCore.getGameTaskPool()
                        val tasks = it.mapNotNull { task ->
                            pool.firstOrNull { it.id == task.id }
                                ?.copy(done = task.done, opened = task.opened)
                        }
                        pvpTasks.addAll(tasks.distinctBy { it.id })
                    }
                } catch (e: Exception) {
                    Timber.e(e)
                    // 修改了task格式
                    clearDayTasks()
                }
            }
        } else {
            pvpTasks.clear()
        }

        val filteredTasks = repo.gameCore.getGameTaskPool()
        if (pvpTasks.isEmpty()) {
            val maxGameProgress = min(
                StoryManager.getMaxAge(), getIntFlowByKey(
                    FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.GAME_PROGRESS.id + "_1",
                    1
                )
            )
            pvpTasks.addAll(filteredTasks.filter { it.isPvpTask() }.filter {
                maxGameProgress >= it.talent.first() && maxGameProgress <= it.talent[1]
            }.shuffled(RANDOM))

            setLongValueByKey(KEY_GAME_PVP_TASK_UPDATE_TIME_IN_MILLIS, getCurrentTime())
            setListObject(KEY_GAME_PVP_TASK, pvpTasks)
        }
    }

    fun getTaskDoneFlow(task: Quest): Boolean {
        if (DebugManager.questDone) return true
        val taskNum = task.num
        when (task.type) {
            QuestEvent.ENDING.id -> {
                return getBooleanFlowByKey(FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.ENDING.id + "_${task.subType.first()}" + "_${task.num}")
            }

            QuestEvent.REPUTATION_LEVEL.id -> {
                val data = AwardManager.toReputationLevelData()
                return if (task.subType.first() == 0) {
                    data.any { it.level >= taskNum }
                } else {
                    data[task.subType.first() - 1].level >= taskNum
                }
            }

            QuestEvent.HAVE_TYPE_ITEM.id -> {
                return if (task.subType.first() == 1) {
                    repo.allyManager.data.size >= taskNum
                } else {
                    false
                }
            }

            QuestEvent.HAVE_STAR_ITEM.id -> {
                return if (task.subType.first() / 10 == 1) {
                    val star = task.subType.first() % 10
                    repo.allyManager.data.filter { it.star >= star }.size >= taskNum
                } else {
                    false
                }
            }

            QuestEvent.PVP_BATTLE.id -> {
                return if (task.subType.first() == 0) {
                    PvpManager.pkWinToday.value + PvpManager.pkLoseToday.value >= taskNum
                } else {
                    PvpManager.pkWinToday.value >= taskNum
                }
            }

            QuestEvent.PVP_RANK.id -> {
                val realRank = lastPvpRanks.value
                return when (task.subType.first()) {
                    1 -> {
                        realRank.getOrNull(0)?.userId == GameApp.instance.getObjectId()
                    }
                    2 -> {
                        realRank.getOrNull(1)?.userId == GameApp.instance.getObjectId()
                    }
                    3 -> {
                        realRank.getOrNull(2)?.userId == GameApp.instance.getObjectId()
                    }
                    4 -> {
                        realRank.getOrNull(3)?.userId == GameApp.instance.getObjectId()
                    }
                    5 -> {
                        realRank.getOrNull(4)?.userId == GameApp.instance.getObjectId()
                    }
                    6 -> {
                        // 6-10名
                        val userIds = if (realRank.size > 5) {
                            realRank.subList(5, min(realRank.size, 10)).map { it.userId }
                        } else {
                            emptyList()
                        }
                        userIds.contains(GameApp.instance.getObjectId())
                    }
                    7 -> {
                        // 11-50名
                        val userIds = if (realRank.size > 10) {
                            realRank.subList(10, min(realRank.size, 50)).map { it.userId }
                        } else {
                            emptyList()
                        }
                        userIds.contains(GameApp.instance.getObjectId())
                    }
                    8 -> {
                        // 51-100名
                        val userIds = if (realRank.size > 50) {
                            realRank.subList(50, min(realRank.size, 100)).map { it.userId }
                        } else {
                            emptyList()
                        }
                        userIds.contains(GameApp.instance.getObjectId())
                    }
                    else -> {
                        false
                    }
                }
            }

            QuestEvent.COST.id -> {
                return AwardManager.keyCost.value >= task.num
            }

            QuestEvent.CHARGE.id -> {
                return SevenDayManager.isChargeTaskDone(task.num)
            }

            else -> {
                val postFix = task.subType.map {
                    if (it == 0) "" else "_$it"
                }.reduce { acc, s -> acc + s }
                val preFix = if (task.isForever()) FOREVER else ""
                return getIntFlowByKey(preFix + KEY_GAME_TASK_PROGRESS + task.type + postFix) - task.needRemoveCount >= taskNum
            }
        }
    }

    fun getTaskProgressFlow(task: Quest): String {
        val taskNum = task.num
        when (task.type) {
            QuestEvent.ENDING.id -> {
                return ""
            }

            QuestEvent.REPUTATION_LEVEL.id -> {
                val data = AwardManager.toReputationLevelData()
                return if (task.subType.first() == 0) {
                    data.maxOf { it.level }.toString() + "/" + taskNum
                } else {
                    data[task.subType.first() - 1].level.toString() + "/" + taskNum
                }
            }

            QuestEvent.HAVE_TYPE_ITEM.id -> {
                return if (task.subType.first() == 1) {
                    repo.allyManager.data.size.toString() + "/" + taskNum
                } else {
                    "0/$taskNum"
                }
            }

            QuestEvent.HAVE_STAR_ITEM.id -> {
                return if (task.subType.first() / 10 == 1) {
                    val star = task.subType.first() % 10
                    repo.allyManager.data.filter { it.star >= star }.size.toString() + "/" + taskNum
                } else {
                    "0/$taskNum"
                }
            }

            QuestEvent.PVP_BATTLE.id -> {
                return if (task.subType.first() == 0) {
                    (PvpManager.pkWinToday.value + PvpManager.pkLoseToday.value).toString() + "/" + taskNum
                } else {
                    (PvpManager.pkWinToday.value).toString() + "/" + taskNum
                }
            }

            QuestEvent.PVP_RANK.id -> {
                return ""
            }

            QuestEvent.COST.id -> {
                return AwardManager.keyCost.value.toString() + "/" + task.num
            }

            QuestEvent.CHARGE.id -> {
                return SevenDayManager.isChargeTaskDoneString(task.num)
            }

            else -> {
                val postFix = task.subType.map {
                    if (it == 0) "" else "_$it"
                }.reduce { acc, s -> acc + s }
                val preFix = if (task.isForever()) FOREVER else ""
                return getIntFlowByKey(preFix + KEY_GAME_TASK_PROGRESS + task.type + postFix).let {
                    "${it - task.needRemoveCount}/${taskNum}"
                }
            }
        }
    }

    suspend fun questReward(quest: Quest, award: Award) {
        checkQuest(quest)
        if (quest.isDailyTask()) {
            val index = dailyTasks.indexOfFirst { it.id == quest.id }
            if (!dailyTasks[index].opened) {
                dailyTasks[index] = dailyTasks[index].copy(opened = true)
                setListObjectSync(KEY_GAME_TASK, dailyTasks)
                val realAward = if (VipManager.isDoubleQuestAward()) {
                    award + award
                } else award
                AwardManager.gainAward(realAward)
                Dialogs.awardDialog.value = realAward
            }
        } else if (quest.isNewTask()) {
            val index = newTasks.indexOfFirst { it.id == quest.id }
            if (!newTasks[index].opened) {
                newTasks[index] = newTasks[index].copy(opened = true)
                setListObjectSync(KEY_NEW_TASK, newTasks)
                AwardManager.gainAward(award)
                Dialogs.awardDialog.value = award
            }
        } else if (quest.isWarPassTask()) {
            val index = warPassTasks.indexOfFirst { it.id == quest.id }
            if (!warPassTasks[index].opened) {
                warPassTasks[index] = warPassTasks[index].copy(opened = true)
                setListObjectSync(KEY_WAR_PASS_TASK, warPassTasks)
                AwardManager.gainAward(award)
                Dialogs.awardDialog.value = award
            }
        }  else if (quest.isWarPass2Task()) {
            val index = warPass2Tasks.indexOfFirst { it.id == quest.id }
            if (!warPass2Tasks[index].opened) {
                warPass2Tasks[index] = warPass2Tasks[index].copy(opened = true)
                setListObjectSync(KEY_WAR_PASS2_TASK, warPass2Tasks)
                AwardManager.gainAward(award)
                Dialogs.awardDialog.value = award
            }
        } else if (quest.isOneTimeTask()) {
            val index = oneTimeTasks.indexOfFirst { it.id == quest.id }
            if (!oneTimeTasks[index].opened) {
                oneTimeTasks[index] = oneTimeTasks[index].copy(opened = true)
                setListObjectSync(KEY_ONE_TIME_GAME_TASK, oneTimeTasks)
                AwardManager.gainAward(award)
                Dialogs.awardDialog.value = award
            }
        } else if (quest.isPvpTask()) {
            val index = pvpTasks.indexOfFirst { it.id == quest.id }
            if (!pvpTasks[index].opened) {
                pvpTasks[index] = pvpTasks[index].copy(opened = true)
                setListObjectSync(KEY_GAME_PVP_TASK, pvpTasks)
                AwardManager.gainAward(award)
                Dialogs.awardDialog.value = award
            }
        } else if (quest.isCollectTask()) {
            val index = SevenDayManager.collectTasks.indexOfFirst { it.id == quest.id }
            if (!SevenDayManager.collectTasks[index].opened) {
                SevenDayManager.collectTasks[index] = SevenDayManager.collectTasks[index].copy(opened = true)
                setListObjectSync(KEY_COLLECT_TASK, SevenDayManager.collectTasks)
                AwardManager.gainAward(award)
                Dialogs.awardDialog.value = award
            }
        } else if (quest.isCostTask()) {
            val index = SevenDayManager.costTasks.indexOfFirst { it.id == quest.id }
            if (!SevenDayManager.costTasks[index].opened) {
                SevenDayManager.costTasks[index] = SevenDayManager.costTasks[index].copy(opened = true)
                setListObjectSync(KEY_COST_TASK, SevenDayManager.costTasks)
                AwardManager.gainAward(award)
                Dialogs.awardDialog.value = award
            }
        } else if (quest.isChargeTask()) {
            val index = SevenDayManager.chargeTasks.indexOfFirst { it.id == quest.id }
            if (!SevenDayManager.chargeTasks[index].opened) {
                SevenDayManager.chargeTasks[index] = SevenDayManager.chargeTasks[index].copy(opened = true)
                setListObjectSync(KEY_CHARGE_TASK, SevenDayManager.chargeTasks)
                AwardManager.gainAward(award)
                Dialogs.awardDialog.value = award
            }
        } else if (quest.isDrawTask()) {
            val index = DrawManager.drawTasks.indexOfFirst { it.id == quest.id }
            if (!DrawManager.drawTasks[index].opened) {
                DrawManager.drawTasks[index] = DrawManager.drawTasks[index].copy(opened = true)
                setListObjectSync(KEY_GAME_DRAW_TASK, DrawManager.drawTasks)
                AwardManager.gainAward(award)
                Dialogs.awardDialog.value = award
            }
        } else {
            error("未知任务类型${quest.taskType}")
        }
    }

    private fun clearDayTasks() {
        dailyTasks.clear()
        mapData.keys.filter {
            it.startsWith(
                KEY_GAME_TASK_PROGRESS
            )
        }.forEach {
            setIntValueByKey(it, 0)
        }
        // todo 每日清理数据放这里
        GiftManager.onRefreshNumTheNextDay()
    }

    fun isTaskDialogShowed(task: Quest): Boolean {
        return getBooleanFlowByKey(KEY_TASK_DIALOG_SHOWED + "_" + task.id)
    }

    fun showTaskDialog(task: Quest) {
        setBooleanValueByKey(KEY_TASK_DIALOG_SHOWED + "_" + task.id, true)
    }

    fun getNewQuestByPageIndex(tasks: List<Quest>, page: Int): List<Quest> {
        return tasks.subListOrEmpty(
            (page) * repo.gameCore.getNewQuestCount(),
            (page + 1) * repo.gameCore.getNewQuestCount()
        )
    }

    fun getNewQuestPageCount(tasks: List<Quest>): Int {
        val pageMax = repo.gameCore.getGameTaskPool()
            .filter { it.isNewTask() }.size / repo.gameCore.getNewQuestCount()
        (0.until(pageMax)).forEach { page ->
            val subTask = getNewQuestByPageIndex(tasks, page)
            if (subTask.any { !it.done }) {
                // 当前页面有任务没完成，那就显示这一页
                return min(pageMax, page + 1)
            }
            if (subTask.isEmpty()) {
                // 当前页面没有任务，说明还没有开始解锁，那就显示上一页
                return min(pageMax, page)
            }
        }
        return pageMax
    }

    fun getInitPageIndex(tasks: SnapshotStateList<Quest>): Int {
        val pageMax = repo.gameCore.getGameTaskPool()
            .filter { it.isNewTask() }.size / repo.gameCore.getNewQuestCount()
        (0.until(pageMax)).forEach { page ->
            val subTask = getNewQuestByPageIndex(tasks, page)
            if (subTask.any { !it.opened }) {
                // 当前页面有任务没领完，那就显示这一页
                return min(pageMax, page)
            }
            if (subTask.isEmpty()) {
                // 当前页面没有任务，说明还没有开始解锁，那就显示上一页
                return max(0, page - 1)
            }
        }
        return 0
    }

    fun canAwardNewQuestByPageIndex(tasks: List<Quest>, pageIndex: Int): Boolean {
        val subTask = getNewQuestByPageIndex(tasks, pageIndex)
        return subTask.isNotEmpty() && subTask.all { it.done }
    }

    fun getNewQuestsByIndex(index: Int): List<Quest> {
        createNewTasks()
        // 完成的任务排前面，已领取的排最后
        return getNewQuestByPageIndex(newTasks, index).map {
            it.copy(done = getTaskDoneFlow(it))
        }.sortedBy { it.order }
    }
}